# Snapchat-Style Map Implementation

This is a new, high-performance map implementation that mimics Snapchat's map style with 3D buildings, vector tiles, and optimized rendering.

## Features

### 1. **3D Buildings**
- Uses MapLibre GL's `fill-extrusion` layer for 3D building rendering
- Buildings are extruded based on their height data from OpenStreetMap
- Smooth transitions when zooming in/out
- Toggle on/off with the building icon button

### 2. **Parks and Vegetation**
- **Bright green parks** - Snapchat-style vibrant green for all parks
- **Grass areas** - Lighter green for meadows and grass
- **Forests** - Darker green for wooded areas
- **Tree symbols** - Animated tree icons in larger parks (zoom 15+)
- **Vibrant water** - Bright cyan water bodies with outlines
- Toggle on/off with the park icon button

### 3. **Vector Tiles**
- Uses Stadia Maps OSM vector tiles for crisp, scalable map data
- Supports both light and dark themes
- Efficient tile loading and caching

### 4. **Custom User Avatar**
- Animated user location marker with bounce effect
- Custom-drawn avatar (blue circle with white border)
- Smooth location updates with GPS tracking

### 5. **Performance Optimizations**
- GPU-accelerated rendering via MapLibre GL
- Efficient vector tile rendering
- Minimal UI overhead
- Smart zoom level management for 3D features

### 6. **Snapchat-like Styling**
- Vibrant colors for water (#00BCD4) and parks (#4CAF50)
- Multiple shades of green for different vegetation types
- Tree symbols in parks at higher zoom levels
- Simplified road styling
- Clean, modern UI controls
- Smooth animations and transitions

## Setup

### 1. Get a Stadia Maps API Key
1. Go to https://stadiamaps.com/
2. Sign up for a free account
3. Create a new API key
4. Replace `YOUR_STADIA_API_KEY` in the code with your actual key

### 2. Platform Setup

#### iOS
Add to `ios/Runner/Info.plist`:
```xml
<key>NSLocationWhenInUseUsageDescription</key>
<string>This app needs access to location when open to show your position on the map.</string>
<key>NSLocationAlwaysUsageDescription</key>
<string>This app needs access to location to show your position on the map.</string>
```

#### Android
Add to `android/app/src/main/AndroidManifest.xml`:
```xml
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
```

## Usage

### Access from Settings
1. Open the app
2. Go to Settings
3. Scroll to "Developer Options"
4. Tap "Test Snapchat-Style Map"

### Direct Navigation
```dart
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => const SnapchatStyleMapScreen(
      showBottomNav: false,
    ),
  ),
);
```

## Customization

### Change Map Style
Update the style URLs in `SnapchatStyleMapScreen`:
```dart
static const String _lightStyleUrl = 'YOUR_LIGHT_STYLE_URL';
static const String _darkStyleUrl = 'YOUR_DARK_STYLE_URL';
```

### Adjust 3D Settings
Modify these constants:
```dart
static const double _minZoomFor3D = 15.0;  // Minimum zoom for 3D buildings
static const double _defaultTilt = 45.0;   // Camera tilt angle
```

### Custom Avatar
The avatar is drawn programmatically in `_addUserAvatarImage()`. You can customize:
- Colors
- Size
- Shape
- Add images or icons

## Performance Tips

1. **Reduce Detail at Low Zoom**
   - 3D buildings only appear at zoom 15+
   - This prevents rendering too many buildings at once

2. **Optimize Style**
   - Use simpler styles with fewer layers
   - Remove unnecessary map features
   - Limit label density

3. **Control Update Frequency**
   - Location updates every 10 meters
   - Smooth camera animations (500ms)

## Comparison with Current Implementation

| Feature | Current Map | Snapchat-Style Map |
|---------|-------------|-------------------|
| Rendering | Flutter Map (CPU) | MapLibre GL (GPU) |
| 3D Buildings | Simulated with shadows | True 3D extrusion |
| Tile Type | Raster | Vector |
| Performance | Good | Excellent |
| Customization | Limited | Full style control |

## Future Enhancements

1. **Friends on Map**
   - Show friend locations with custom avatars
   - Friend clusters at low zoom

2. **Heat Maps**
   - Show popular areas
   - Music activity heat maps

3. **Custom Markers**
   - Music pins with 3D effects
   - Animated pin drops

4. **AR Mode**
   - Camera overlay for AR navigation
   - 3D music visualization in AR

## Troubleshooting

### Map Not Loading
- Check API key is valid
- Ensure internet connection
- Check console for errors

### 3D Buildings Not Showing
The 3D buildings feature depends on:
1. **Building height data** in the map style
2. **Proper source configuration**
3. **Zoom level** (must be 15+)

**Solutions:**

1. **Use MapTiler (Recommended for 3D)**
   - Sign up at https://cloud.maptiler.com/
   - Get a free API key
   - In the code, set:
     ```dart
     static const String _mapTilerKey = 'YOUR_KEY';
     static const bool _useMapTiler = true;
     ```
   - MapTiler styles include proper building height data

2. **Check Debug Output**
   - Run the app and check console for errors
   - Look for "Successfully added 3D building layer" or error messages
   - The app tries multiple source names (openmaptiles, composite)

3. **Manual Style Adjustment**
   - Some styles need the building layer added manually
   - The app automatically tries to add it on style load
   - Use the 3D toggle button to enable/disable

4. **Camera Tilt**
   - Use the tilt button (circle icon) to adjust camera angle
   - 3D buildings are more visible at 45-60° tilt
   - The app automatically tilts when enabling 3D

### Performance Issues
- Reduce tilt angle
- Increase minimum zoom for 3D
- Simplify map style

### Location Not Working
- Check location permissions
- Ensure GPS is enabled
- Test on real device (not simulator) 
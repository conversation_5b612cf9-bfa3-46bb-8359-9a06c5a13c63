import 'package:flutter/material.dart';
import 'package:maplibre_gl/maplibre_gl.dart';
import 'package:provider/provider.dart';
import 'dart:async';
import 'dart:math' as math;
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/services.dart';

import '../constants/map_style.dart';
import '../models/map_style_config.dart';
import '../../../providers/theme_provider.dart';
import '../../../config/constants.dart';

class MapStyleManager {
  final BuildContext context;
  MaplibreMapController? _mapController;
  
  // Current map style state
  MapStyle _currentMapStyle = MapStyle.standard;
  bool _isBlackAndWhiteActive = false;
  
  // Style constants
  static final String _stadiaApiKey = AppConstants.stadiaApiKey;
  
  // Map style configurations
  static final Map<MapStyle, MapStyleConfig> _mapStyles = {
    MapStyle.standard: MapStyleConfig(
      name: 'Standard',
      lightUrl: 'https://tiles.stadiamaps.com/styles/osm_bright.json?api_key=$_stadiaApiKey',
      darkUrl: 'https://tiles.stadiamaps.com/styles/alidade_smooth_dark.json?api_key=$_stadiaApiKey',
      icon: Icons.map,
      accentColor: Colors.blue,
    ),
    MapStyle.neon: MapStyleConfig(
      name: 'Neon',
      lightUrl: 'https://tiles.stadiamaps.com/styles/alidade_smooth_dark.json?api_key=$_stadiaApiKey',
      darkUrl: 'https://tiles.stadiamaps.com/styles/alidade_smooth_dark.json?api_key=$_stadiaApiKey',
      icon: Icons.electric_bolt,
      accentColor: Colors.cyan,
    ),
    MapStyle.cyberpunk: MapStyleConfig(
      name: 'Cyberpunk',
      lightUrl: 'https://tiles.stadiamaps.com/styles/alidade_smooth_dark.json?api_key=$_stadiaApiKey',
      darkUrl: 'https://tiles.stadiamaps.com/styles/alidade_smooth_dark.json?api_key=$_stadiaApiKey',
      icon: Icons.computer,
      accentColor: Colors.purple,
    ),
    MapStyle.oldSchool: MapStyleConfig(
      name: 'Old School',
      lightUrl: 'https://tiles.stadiamaps.com/styles/stamen_toner_lite.json?api_key=$_stadiaApiKey',
      darkUrl: 'https://tiles.stadiamaps.com/styles/stamen_toner.json?api_key=$_stadiaApiKey',
      icon: Icons.history,
      accentColor: Colors.grey,
    ),
    MapStyle.watercolor: MapStyleConfig(
      name: 'Watercolor',
      lightUrl: 'https://tiles.stadiamaps.com/styles/stamen_watercolor.json?api_key=$_stadiaApiKey',
      darkUrl: 'https://tiles.stadiamaps.com/styles/stamen_watercolor.json?api_key=$_stadiaApiKey',
      icon: Icons.brush,
      accentColor: Colors.brown,
    ),
    MapStyle.retro: MapStyleConfig(
      name: 'Retro',
      lightUrl: 'https://tiles.stadiamaps.com/styles/alidade_smooth.json?api_key=$_stadiaApiKey',
      darkUrl: 'https://tiles.stadiamaps.com/styles/alidade_smooth.json?api_key=$_stadiaApiKey',
      icon: Icons.games,
      accentColor: Colors.pink,
    ),
    MapStyle.matrix: MapStyleConfig(
      name: 'Matrix',
      lightUrl: 'https://tiles.stadiamaps.com/styles/alidade_smooth_dark.json?api_key=$_stadiaApiKey',
      darkUrl: 'https://tiles.stadiamaps.com/styles/alidade_smooth_dark.json?api_key=$_stadiaApiKey',
      icon: Icons.code,
      accentColor: Colors.green,
    ),
    MapStyle.vaporwave: MapStyleConfig(
      name: 'Vaporwave',
      lightUrl: 'https://tiles.stadiamaps.com/styles/alidade_smooth_dark.json?api_key=$_stadiaApiKey',
      darkUrl: 'https://tiles.stadiamaps.com/styles/alidade_smooth_dark.json?api_key=$_stadiaApiKey',
      icon: Icons.waves,
      accentColor: Colors.pinkAccent,
    ),
    MapStyle.minimal: MapStyleConfig(
      name: 'Minimal',
      lightUrl: 'https://tiles.stadiamaps.com/styles/osm_bright.json?api_key=$_stadiaApiKey',
      darkUrl: 'https://tiles.stadiamaps.com/styles/alidade_smooth_dark.json?api_key=$_stadiaApiKey',
      icon: Icons.circle_outlined,
      accentColor: Colors.grey,
    ),
    MapStyle.futuristic: MapStyleConfig(
      name: 'Futuristic',
      lightUrl: 'https://tiles.stadiamaps.com/styles/alidade_smooth_dark.json?api_key=$_stadiaApiKey',
      darkUrl: 'https://tiles.stadiamaps.com/styles/alidade_smooth_dark.json?api_key=$_stadiaApiKey',
      icon: Icons.grid_on,
      accentColor: Colors.cyan,
    ),
  };

  MapStyleManager(this.context);

  // Getters
  MapStyle get currentMapStyle => _currentMapStyle;
  bool get isBlackAndWhiteActive => _isBlackAndWhiteActive;
  Map<MapStyle, MapStyleConfig> get mapStyles => _mapStyles;

  // Initialize with map controller
  void initialize(MaplibreMapController controller) {
    _mapController = controller;
  }

  // Get current style URL based on theme
  String getCurrentStyleUrl(bool isDarkMode) {
    final styleConfig = _mapStyles[_currentMapStyle];
    if (styleConfig == null) {
      return _mapStyles[MapStyle.standard]!.lightUrl;
    }
    return isDarkMode ? styleConfig.darkUrl : styleConfig.lightUrl;
  }

  // Change map style
  void changeMapStyle(MapStyle newStyle) {
    if (newStyle == _currentMapStyle) return;
    
    // Clean up current style layers before switching
    if (_mapController != null) {
      cleanupAllStyleLayers();
    }
    
    _currentMapStyle = newStyle;
  }

  // Set black and white state for retro style
  void setBlackAndWhiteActive(bool active) {
    if (_isBlackAndWhiteActive == active) return;
    
    debugPrint('📺 Setting B&W active: $active (was: $_isBlackAndWhiteActive)');
    _isBlackAndWhiteActive = active;
  }

  // Clean up all style-specific layers to prevent overlaps
  void cleanupAllStyleLayers() {
    if (_mapController == null) return;
    
    final allStyleLayers = [
      // Neon style layers
      'neon-roads', 'neon-buildings', 'neon-glow',
      
      // Cyberpunk style layers
      'cyberpunk-roads', 'cyberpunk-roads-base', 'cyberpunk-roads-neon', 'cyberpunk-data-flow', 
      'cyberpunk-roads-glow', 'cyberpunk-buildings', 'cyberpunk-water', 'cyberpunk-water-edges',
      'cyberpunk-parks', 'cyberpunk-park-grid', 'cyberpunk-vegetation', 'cyberpunk-vegetation-edges',
      'cyberpunk-effects',
      
      // Retro style layers (colored)
      'retro-roads', 'retro-buildings', 'retro-water', 'retro-parks', 'retro-grass', 'retro-forest',
      
      // Retro style layers (black and white)
      'retro-roads-bw', 'retro-buildings-bw', 'retro-water-bw', 'retro-parks-bw', 'retro-grass-bw', 'retro-forest-bw',
      'retro-landcover-bw', 'retro-place-labels-bw', 'retro-road-labels-bw',
      
      // Matrix style layers
      'matrix-roads', 'matrix-buildings', 'matrix-grass', 'matrix-forest', 'matrix-parks', 'matrix-road-outlines',
      
      // Vaporwave style layers
      'vaporwave-roads', 'vaporwave-buildings',
      
      // Minimal style layers
      'minimal-roads', 'minimal-buildings',
      
      // Old school style layers
      'oldschool-roads', 'oldschool-buildings', 'oldschool-buildings-2d',
      
      // Watercolor style layers
      'watercolor-buildings',
      
      // Futuristic style layers
      'futuristic-background', 'futuristic-grid', 'futuristic-accents',
      'futuristic-buildings', 'futuristic-building-edges', 'futuristic-building-accent-edges',
      'futuristic-building-glow', 'futuristic-water', 'futuristic-grass', 'futuristic-forest',
      'futuristic-parks', 'futuristic-road-outlines',
      
      // Common building layers
      'building-3d', 'building-base-soft', 'building-edge-blur',
      'building-3d-residential', 'building-3d-commercial', 'building-3d-industrial',
      'building-labels', 'building-ground-shadows', 'building-ambient-occlusion', 'building-atmosphere',
      'building-atmosphere-simple', 'building', 'building-outline',
      
      // Building icons and labels
      'building-icons', 'building-labels',
      
      // Text and label layers
      'road-labels', 'place-labels', 'water-labels', 'poi-labels',
      
      // Glow and aura layers
      'aura-border-layer', 'glow-pulse-layer', 'glow-fill-0', 'glow-fill-1', 
      'glow-fill-2', 'glow-fill-3',
    ];
    
    for (final layerId in allStyleLayers) {
      _mapController!.removeLayer(layerId).catchError((e) {
        // Layer might not exist, that's okay
      });
    }
    
    // Also clean up glow-related sources
    final sourcesToRemove = [
      'glow-polygons-source',
      'border-polygons-source', 
      'pulse-polygons-source',
    ];
    
    for (final sourceId in sourcesToRemove) {
      _mapController!.removeSource(sourceId).catchError((e) {
        // Source might not exist, that's okay
      });
    }
    
    debugPrint('🧹 Cleaned up all style-specific layers and sources');
  }
  
  // Remove all building layers to prevent z-fighting
  void removeAllBuildingLayers() {
    if (_mapController == null) return;
    
    final buildingLayers = [
      // Default building layers
      'building-3d', 'building-base-soft', 'building-edge-blur',
      'building-3d-residential', 'building-3d-commercial', 'building-3d-industrial',
      'building', 'building-outline',
      
      // Effect layers
      'building-labels', 'building-ground-shadows', 'building-ambient-occlusion', 'building-atmosphere',
      'building-atmosphere-simple',
      
      // Style-specific building layers
      'neon-building-3d', 'building-neon-glow', 'building-neon-edges',
      'building-base-glow', 'building-ground-glow',
      'cyberpunk-buildings', 'retro-buildings', 'matrix-buildings',
      'vaporwave-buildings', 'minimal-buildings', 'oldschool-buildings',
      'oldschool-buildings-2d', 'watercolor-buildings',
      'futuristic-buildings', 'futuristic-building-edges', 'futuristic-building-glow',
    ];
    
    for (final layer in buildingLayers) {
      _mapController!.removeLayer(layer).catchError((e) {
        // Layer might not exist, that's okay
      });
    }
    
    debugPrint('🏢 Removed all building layers to prevent z-fighting');
  }

  // Ensure vector source exists for building data
  Future<void> ensureVectorSource() async {
    if (_mapController == null) return;
    
    try {
      await _mapController!.addSource(
        'openmaptiles',
        VectorSourceProperties(
          url: 'https://tiles.stadiamaps.com/data/openmaptiles.json?api_key=$_stadiaApiKey',
        ),
      );
      debugPrint('✅ Added openmaptiles vector source');
    } catch (e) {
      // Source might already exist, that's okay
      if (e.toString().contains('already exists') || e.toString().contains('sourceAlreadyExists')) {
        debugPrint('✅ Openmaptiles source already exists');
      } else {
        debugPrint('⚠️ Could not add openmaptiles source: $e');
      }
    }
  }

  // Add 3D building layer
  void add3DBuildingLayer() {
    if (_mapController == null) return;
    
    try {
      // First, let's check if there's already a building layer and remove it
      _mapController!.removeLayer('building-3d').catchError((e) {
        // Layer might not exist, that's okay
        debugPrint('No existing building-3d layer to remove');
      });
      
      // Remove any other building-related layers we might add
      final layersToRemove = [
        'building-3d-residential',
        'building-3d-commercial',
        'building-3d-industrial',
        'building-labels',
        'building-ground-shadows',
        'building-ambient-occlusion',
        'building-atmosphere',
        'building-atmosphere-simple',
        'building-base-soft',
        'building', // Remove any default building layer
        'building-outline', // Remove any outline layers
      ];
      
      for (final layer in layersToRemove) {
        _mapController!.removeLayer(layer).catchError((e) {
          // Layers might not exist, that's okay
        });
      }
      
      // For iOS/macOS compatibility, use a single simplified 3D building layer
      // with height-based coloring instead of complex filters
      _mapController!.addFillExtrusionLayer(
        'openmaptiles',
        'building-3d',
        FillExtrusionLayerProperties(
          // Softer, more organic building colors with smooth gradients
          fillExtrusionColor: [
            'interpolate',
            ['exponential', 1.5], // Use exponential interpolation for smoother color transitions
            ['get', 'render_height'],
            0, '#F5F8FC',      // Almost white with blue tint
            5, '#E8EFF7',      // Very soft blue-white
            10, '#DCE7F2',     // Soft powdery blue
            15, '#D0DFED',     // Light sky blue
            20, '#C4D7E8',     // Gentle blue
            30, '#B8CFE3',     // Soft ocean blue
            40, '#ACC7DE',     // Muted blue-gray
            50, '#A0BFD9',     // Dusty blue
            60, '#94B7D4',     // Gentle steel blue
            80, '#88AFCF',     // Soft sapphire
            100, '#7CA7CA',    // Muted royal blue
            120, '#709FC5',    // Gentle deep blue
            150, '#6497C0',    // Soft midnight blue
            200, '#588FBB',    // Muted ultra blue
          ],
          fillExtrusionHeight: [
            'interpolate',
            ['linear'],
            ['zoom'],
            14, 0,
            14.5, ['*', ['get', 'render_height'], 0.5],
            15, ['*', ['get', 'render_height'], 1.0],
            15.5, ['*', ['get', 'render_height'], 1.5],
            16, ['*', ['get', 'render_height'], 2.0],
            16.5, ['*', ['get', 'render_height'], 2.25],
            17, ['*', ['get', 'render_height'], 2.5],
            17.5, ['*', ['get', 'render_height'], 2.75],
            18, ['*', ['get', 'render_height'], 3.0],
          ],
          fillExtrusionBase: [
            'interpolate',
            ['linear'],
            ['zoom'],
            14, 0,
            14.5, ['*', ['get', 'render_min_height'], 0.25],
            15, ['*', ['get', 'render_min_height'], 0.5],
            15.5, ['*', ['get', 'render_min_height'], 0.75],
            16, ['get', 'render_min_height']
          ],
          fillExtrusionOpacity: [
            'interpolate',
            ['exponential', 1.2],
            ['zoom'],
            14, 0.35,
            14.5, 0.65,
            15, 0.8,
            15.5, 0.85,
            16, 0.9,
            16.5, 0.93,
            17, 0.95,
            18, 0.95,
          ],
          // Add vertical gradient for depth - this creates soft edges
          fillExtrusionVerticalGradient: true,
        ),
        sourceLayer: 'building',
        minzoom: 14.0,
        // More precise filter to avoid overlapping buildings
        filter: ['all',
          ['>', ['get', 'render_height'], 0],
          ['!=', ['get', 'type'], 'underground'], // Exclude underground structures
        ],
        belowLayerId: 'individual-pins-layer',
      ).then((_) {
        debugPrint('Successfully added simplified 3D building layer');
        

        
        // Add atmospheric effects for realism
        addAtmosphericEffects();
        
        // Add simple building labels for iOS compatibility
        addBuildingLabels();
      }).catchError((e) {
        debugPrint('Error adding 3D building layer: $e');
        // Try alternative approach
        tryAlternative3DBuildings();
      });
      
      // Add a subtle base layer for buildings to soften their appearance
      _mapController!.addFillLayer(
        'openmaptiles',
        'building-base-soft',
        FillLayerProperties(
          fillColor: [
            'interpolate',
            ['exponential', 2.0], // Stronger exponential curve for smoother gradients
            ['get', 'render_height'],
            0, '#FAFBFC',
            20, '#F5F7FA',
            50, '#EEF2F6',
            100, '#E5EBF2',
            150, '#DCE4EE',
            200, '#D3DDEA',
          ],
          fillOpacity: [
            'interpolate',
            ['exponential', 1.5],
            ['zoom'],
            14, 0,
            14.5, 0.1,
            15, 0.15,
            15.5, 0.2,
            16, 0.25,
            16.5, 0.3,
            17, 0.35,
          ],
          fillAntialias: true, // This helps smooth the edges
        ),
        sourceLayer: 'building',
        minzoom: 14.0,
        filter: ['all',
          ['>', ['get', 'render_height'], 0],
          ['!=', ['get', 'type'], 'underground'],
        ],
        belowLayerId: 'individual-pins-layer',
      ).catchError((e) {
        debugPrint('Could not add building base layer: $e');
      });
      
      // Add edge softening layer
      _mapController!.addFillLayer(
        'openmaptiles',
        'building-edge-blur',
        FillLayerProperties(
          fillColor: [
            'interpolate',
            ['exponential', 1.8],
            ['get', 'render_height'],
            0, '#FFFFFF',
            100, '#F8F9FA',
            200, '#F0F2F5',
          ],
          fillOpacity: [
            'interpolate',
            ['exponential', 1.5],
            ['zoom'],
            15, 0.05,
            16, 0.08,
            17, 0.1,
            18, 0.08,
          ],
          fillAntialias: true,
          fillTranslate: [0.5, 0.5], // Tiny offset for edge blur effect
          fillTranslateAnchor: 'map',
        ),
        sourceLayer: 'building',
        minzoom: 15.0,
        filter: ['all',
          ['>', ['get', 'render_height'], 0],
          ['!=', ['get', 'type'], 'underground'],
        ],
        belowLayerId: 'individual-pins-layer',
      ).catchError((e) {
        debugPrint('Could not add edge blur layer: $e');
      });
      
    } catch (e) {
      debugPrint('Error in 3D building setup: $e');
    }
  }



  // Add atmospheric effects for photorealistic appearance
  void addAtmosphericEffects() {
    if (_mapController == null) return;
    
    try {
      // Add glassmorphism atmosphere effect layer
      _mapController!.addFillExtrusionLayer(
        'openmaptiles',
        'building-atmosphere',
        FillExtrusionLayerProperties(
          fillExtrusionColor: '#E8F0FF', // Light blue-white glassmorphism fog
          fillExtrusionHeight: [
            'interpolate',
            ['linear'],
            ['zoom'],
            14, 0,
            14.5, ['*', ['get', 'render_height'], 0.25],
            15, ['*', ['get', 'render_height'], 0.5],
            15.5, ['*', ['get', 'render_height'], 0.75],
            16, ['get', 'render_height'],
            16.5, ['*', ['get', 'render_height'], 1.1],
            17, ['*', ['get', 'render_height'], 1.2],
          ],
          fillExtrusionBase: [
            'interpolate',
            ['linear'],
            ['zoom'],
            14, 0,
            14.5, ['*', ['get', 'render_min_height'], 0.25],
            15, ['*', ['get', 'render_min_height'], 0.5],
            15.5, ['*', ['get', 'render_min_height'], 0.75],
            16, ['get', 'render_min_height']
          ],
          fillExtrusionOpacity: [
            'interpolate',
            ['linear'],
            ['zoom'],
            14, 0.12,
            15, 0.08,
            16, 0.05,
            17, 0.03,
            18, 0.01,
          ],
        ),
        sourceLayer: 'building',
        minzoom: 14.0,
        filter: ['>', ['get', 'render_height'], 0],
        belowLayerId: 'individual-pins-layer',
      ).catchError((e) {
        debugPrint('Could not add atmospheric effects: $e');
      });
      
      debugPrint('🌫️ Added atmospheric effects for realism');
    } catch (e) {
      debugPrint('Error adding atmospheric effects: $e');
    }
  }

  // Add building labels for 3D buildings
  void addBuildingLabels() {
    if (_mapController == null) return;
    
    // Add enhanced building labels
    _mapController!.addSymbolLayer(
      'openmaptiles',
      'building-labels',
      SymbolLayerProperties(
        // Text field with proper formatting
        textField: [
          'format',
          ['upcase', ['get', 'name']], {},
          '\n', {},
          [
            'case',
            ['has', 'addr:housenumber'], ['get', 'addr:housenumber'],
            ''
          ], {'font-scale': 0.8}
        ],
        // Using common fonts with fallbacks
        textFont: ['literal', ['Open Sans Bold', 'Arial Unicode MS Bold']],
        textSize: [
          'interpolate',
          ['linear'],
          ['zoom'],
          16, 10,
          17, 12,
          18, 14,
          19, 16,
        ],
        textColor: '#2C3E50',
        textHaloColor: '#FFFFFF',
        textHaloWidth: 2.0,
        textHaloBlur: 0.5,
        textAnchor: 'top', // Place text above icon
        textOffset: [0, 0.5], // Slight offset down from anchor
        symbolPlacement: 'point',
        textAllowOverlap: false,
        textIgnorePlacement: false,
        symbolSpacing: 250,
        // Enhanced text styling
        textJustify: 'center',
        textMaxWidth: 10,
        textLineHeight: 1.2,
        // Make text more visible
        textOpacity: [
          'interpolate',
          ['linear'],
          ['zoom'],
          16, 0.7,
          17, 0.85,
          18, 1.0,
        ],
      ),
      sourceLayer: 'building',
      minzoom: 16.0,
      // Enhanced filter - show labels for important/tall buildings
      filter: ['all',
        ['has', 'name'],
        ['any',
          ['>', ['get', 'render_height'], 20], // Tall buildings
          ['in', ['get', 'building'], ['literal', [
            'hospital', 'school', 'university', 'church', 'retail', 
            'office', 'government', 'hotel', 'museum', 'library',
            'bank', 'restaurant', 'stadium', 'mall', 'commercial'
          ]]], // Important building types
        ],
      ],
      belowLayerId: 'individual-pins-layer',
    ).then((_) {
      debugPrint('🏷️ Added enhanced building labels');
    }).catchError((e) {
      debugPrint('Could not add building labels: $e');
    });
  }
  
  // Try alternative 3D buildings approach
  void tryAlternative3DBuildings() {
    if (_mapController == null) return;
    
    try {
      // Some styles might use different source names
      // This is a fallback with simpler but vibrant styling
      _mapController!.addFillExtrusionLayer(
        'composite',
        'building-3d-alt',
        FillExtrusionLayerProperties(
          fillExtrusionColor: [
            'interpolate',
            ['linear'],
            ['get', 'height'],
            0, '#FF9800',     // Orange
            20, '#FF5722',    // Deep orange
            40, '#E91E63',    // Pink
            60, '#9C27B0',    // Purple
            80, '#673AB7',    // Deep purple
            100, '#3F51B5',   // Indigo
            150, '#2196F3',   // Blue
            200, '#00BCD4',   // Cyan
          ],
          fillExtrusionHeight: [
            'interpolate',
            ['linear'],
            ['zoom'],
            15, 0,
            16, ['*', ['get', 'height'], 2]
          ],
          fillExtrusionBase: 0,
          fillExtrusionOpacity: 0.9,
          fillExtrusionVerticalGradient: true,
        ),
        sourceLayer: 'building',
        minzoom: 15.0,
        belowLayerId: 'individual-pins-layer',
      );
      debugPrint('Added alternative 3D building layer');
      

    } catch (e) {
      debugPrint('Alternative 3D building layer also failed: $e');
    }
  }

  // Set the map controller
  void setMapController(MaplibreMapController controller) {
    _mapController = controller;
  }
  
  // Apply neon effects to the map
  void applyNeonEffects() {
    if (_mapController == null) return;
    
    debugPrint('🎨 Applying neon effects...');
    
    try {
      // Remove existing custom road layers
      final layersToRemove = [
        'road-surface',
        'road-edge',
        'road-markings',
        'neon-roads-glow-outer',
        'neon-roads-glow-inner',
        'neon-roads-main',
      ];
      
      for (final layer in layersToRemove) {
        _mapController!.removeLayer(layer).catchError((e) {
          // Layer might not exist, that's okay
        });
      }
      
      // Add outer glow for major roads
      _mapController!.addLineLayer(
        'openmaptiles',
        'neon-roads-glow-outer',
        LineLayerProperties(
          lineColor: [
            'case',
            ['==', ['get', 'class'], 'motorway'], '#00ffff',
            ['==', ['get', 'class'], 'trunk'], '#00ffff',
            ['==', ['get', 'class'], 'primary'], '#ff00ff',
            ['==', ['get', 'class'], 'secondary'], '#ff00aa',
            '#ff00ff'
          ],
          lineWidth: [
            'interpolate',
            ['linear'],
            ['zoom'],
            10, 3,
            14, 6,
            18, 12,
          ],
          lineOpacity: 0.2,
          lineBlur: 8,
        ),
        sourceLayer: 'transportation',
        minzoom: 10.0,
      ).catchError((e) {
        debugPrint('Error adding neon glow outer: $e');
      });
      
      // Add inner glow for major roads
      _mapController!.addLineLayer(
        'openmaptiles',
        'neon-roads-glow-inner',
        LineLayerProperties(
          lineColor: [
            'case',
            ['==', ['get', 'class'], 'motorway'], '#00ffff',
            ['==', ['get', 'class'], 'trunk'], '#00ffff',
            ['==', ['get', 'class'], 'primary'], '#ff00ff',
            ['==', ['get', 'class'], 'secondary'], '#ff00aa',
            '#ff00ff'
          ],
          lineWidth: [
            'interpolate',
            ['linear'],
            ['zoom'],
            10, 2,
            14, 4,
            18, 8,
          ],
          lineOpacity: 0.4,
          lineBlur: 4,
        ),
        sourceLayer: 'transportation',
        minzoom: 10.0,
      ).catchError((e) {
        debugPrint('Error adding neon glow inner: $e');
      });
      
      // Add main neon road lines
      _mapController!.addLineLayer(
        'openmaptiles',
        'neon-roads-main',
        LineLayerProperties(
          lineColor: [
            'case',
            ['==', ['get', 'class'], 'motorway'], '#00ffff',
            ['==', ['get', 'class'], 'trunk'], '#00ffff',
            ['==', ['get', 'class'], 'primary'], '#ff00ff',
            ['==', ['get', 'class'], 'secondary'], '#ff00aa',
            ['==', ['get', 'class'], 'tertiary'], '#ff66ff',
            '#ff00ff'
          ],
          lineWidth: [
            'interpolate',
            ['linear'],
            ['zoom'],
            10, 0.5,
            14, 1.5,
            18, 4,
          ],
          lineOpacity: 1.0,
        ),
        sourceLayer: 'transportation',
        minzoom: 10.0,
      ).catchError((e) {
        debugPrint('Error adding neon main roads: $e');
      });
      
      // Apply neon colors to buildings if 3D buildings are enabled
      _applyNeonBuildingColors();
      
      debugPrint('🎨 Neon effects applied successfully');
    } catch (e) {
      debugPrint('Error applying neon effects: $e');
    }
  }
  
  // Apply cyberpunk effects to the map
  void applyCyberpunkEffects() {
    if (_mapController == null) return;
    debugPrint('🤖 Applying cyberpunk effects...');
    
    try {
      // Remove existing building layers first
      removeAllBuildingLayers();
      
      // Dark base roads (circuit board substrate)
      _mapController!.addLineLayer(
        'openmaptiles',
        'cyberpunk-roads-base',
        LineLayerProperties(
          lineColor: '#0a0a0a', // Very dark base
          lineWidth: [
            'interpolate',
            ['linear'],
            ['zoom'],
            10, 2,
            14, 6,
            18, 12,
          ],
          lineOpacity: 0.9,
        ),
        sourceLayer: 'transportation',
        minzoom: 10.0,
      ).catchError((e) => debugPrint('Error adding cyberpunk road base: $e'));
      
      // Bright neon circuit traces on roads
      _mapController!.addLineLayer(
        'openmaptiles',
        'cyberpunk-roads-neon',
        LineLayerProperties(
          lineColor: [
            'case',
            ['==', ['get', 'class'], 'motorway'], '#ff0080', // Hot pink/magenta for major roads
            ['==', ['get', 'class'], 'trunk'], '#ff0080',
            ['==', ['get', 'class'], 'primary'], '#00ffff', // Cyan for primary
            ['==', ['get', 'class'], 'secondary'], '#8000ff', // Purple for secondary
            '#ff4080' // Pink for minor roads
          ],
          lineWidth: [
            'interpolate',
            ['linear'],
            ['zoom'],
            10, 0.8,
            14, 2,
            18, 4,
          ],
          lineOpacity: 0.95,
        ),
        sourceLayer: 'transportation',
        minzoom: 10.0,
      ).catchError((e) => debugPrint('Error adding cyberpunk neon roads: $e'));
      
      // Outer glow for roads (cyberpunk atmosphere)
      _mapController!.addLineLayer(
        'openmaptiles',
        'cyberpunk-roads-glow',
        LineLayerProperties(
          lineColor: [
            'case',
            ['==', ['get', 'class'], 'motorway'], '#ff0080',
            ['==', ['get', 'class'], 'trunk'], '#ff0080',
            ['==', ['get', 'class'], 'primary'], '#00ffff',
            ['==', ['get', 'class'], 'secondary'], '#8000ff',
            '#ff4080'
          ],
          lineWidth: [
            'interpolate',
            ['linear'],
            ['zoom'],
            10, 4,
            14, 12,
            18, 24,
          ],
          lineOpacity: 0.15,
          lineBlur: 8,
        ),
        sourceLayer: 'transportation',
        minzoom: 10.0,
        belowLayerId: 'cyberpunk-roads-base',
      ).catchError((e) => debugPrint('Error adding cyberpunk road glow: $e'));
      
      // Add cyberpunk 3D buildings if enabled
      _mapController!.addFillExtrusionLayer(
        'openmaptiles',
        'cyberpunk-buildings',
        FillExtrusionLayerProperties(
          fillExtrusionColor: [
            'interpolate',
            ['linear'],
            ['get', 'render_height'],
            0, '#1a0033',    // Very dark purple base
            10, '#330066',   // Dark purple
            20, '#4d0099',   // Medium purple
            30, '#6600cc',   // Bright purple
            50, '#8000ff',   // Electric purple
            70, '#ff0080',   // Hot pink/magenta
            100, '#ff4080',  // Bright pink for skyscrapers
            150, '#00ffff',  // Cyan for mega towers
          ],
          fillExtrusionHeight: [
            'interpolate',
            ['linear'],
            ['zoom'],
            14, 0,
            14.5, ['*', ['get', 'render_height'], 0.8],
            15, ['*', ['get', 'render_height'], 1.5],
            16, ['*', ['get', 'render_height'], 2.5],
            17, ['*', ['get', 'render_height'], 3.5],
            18, ['*', ['get', 'render_height'], 4.5],
          ],
          fillExtrusionBase: [
            'interpolate',
            ['linear'],
            ['zoom'],
            14, 0,
            15, ['get', 'render_min_height']
          ],
          fillExtrusionOpacity: 0.85,
          fillExtrusionVerticalGradient: true,
        ),
        sourceLayer: 'building',
        minzoom: 14.0,
        filter: ['>', ['get', 'render_height'], 0],
        belowLayerId: 'individual-pins-layer',
      ).catchError((e) => debugPrint('Error adding cyberpunk buildings: $e'));
      
      // Add cyberpunk water (dark reflective surfaces)
      _mapController!.addFillLayer(
        'openmaptiles',
        'cyberpunk-water',
        FillLayerProperties(
          fillColor: '#000033', // Very dark blue, almost black
          fillOpacity: 0.95,
        ),
        sourceLayer: 'water',
        minzoom: 0.0,
        belowLayerId: 'individual-pins-layer',
      ).catchError((e) => debugPrint('Error adding cyberpunk water: $e'));
      
      // Add neon edge lighting for water
      _mapController!.addLineLayer(
        'openmaptiles',
        'cyberpunk-water-edges',
        LineLayerProperties(
          lineColor: '#00ffff', // Cyan edge lighting
          lineWidth: 2,
          lineOpacity: 0.6,
          lineBlur: 3,
        ),
        sourceLayer: 'water',
        minzoom: 12.0,
        belowLayerId: 'individual-pins-layer',
      ).catchError((e) => debugPrint('Error adding cyberpunk water edges: $e'));
      
      debugPrint('🤖 Cyberpunk effects applied successfully');
    } catch (e) {
      debugPrint('Error applying cyberpunk effects: $e');
    }
  }
  
  // Apply retro effects to the map
  void applyRetroEffects() {
    if (_mapController == null) return;
    
    debugPrint('🎮 Applying retro effects...');
    
    try {
      // Remove existing building layers to prevent z-fighting
      removeAllBuildingLayers();
      
      // Vibrant retro roads with classic 80s colors
      _mapController!.addLineLayer(
        'openmaptiles',
        'retro-roads',
        LineLayerProperties(
          lineColor: [
            'case',
            ['==', ['get', 'class'], 'motorway'], '#FF6B9D',  // Hot pink
            ['==', ['get', 'class'], 'trunk'], '#FF6B9D',     // Hot pink
            ['==', ['get', 'class'], 'primary'], '#4ECDC4',   // Teal
            ['==', ['get', 'class'], 'secondary'], '#45B7D1', // Sky blue
            '#96CEB4'  // Mint green
          ],
          lineWidth: [
            'interpolate',
            ['linear'],
            ['zoom'],
            10, 1,
            14, 2.5,
            18, 5,
          ],
          lineOpacity: 0.9,
        ),
        sourceLayer: 'transportation',
        minzoom: 10.0,
        belowLayerId: 'individual-pins-layer',
      ).catchError((e) => debugPrint('Error adding retro roads: $e'));
      
      // Add retro buildings
      _mapController!.addFillExtrusionLayer(
        'openmaptiles',
        'retro-buildings',
        FillExtrusionLayerProperties(
          fillExtrusionColor: [
            'interpolate',
            ['linear'],
            ['get', 'render_height'],
            0, '#FF6B9D',   // Hot pink
            20, '#4ECDC4',  // Teal
            40, '#45B7D1',  // Sky blue
            60, '#96CEB4',  // Mint green
            80, '#FECA57',  // Golden yellow
            100, '#FF9FF3', // Light pink
          ],
          fillExtrusionHeight: [
            'interpolate',
            ['linear'],
            ['zoom'],
            15, ['*', ['get', 'render_height'], 1.2],
            18, ['*', ['get', 'render_height'], 2.5],
          ],
          fillExtrusionBase: [
            'interpolate',
            ['linear'],
            ['zoom'],
            15, 0,
            16, ['get', 'render_min_height']
          ],
          fillExtrusionOpacity: 0.8,
          fillExtrusionVerticalGradient: true,
        ),
        sourceLayer: 'building',
        minzoom: 14.0,
        filter: ['all',
          ['>', ['get', 'render_height'], 0],
          ['!=', ['get', 'type'], 'underground'],
        ],
        belowLayerId: 'individual-pins-layer',
      ).catchError((e) => debugPrint('Error adding retro buildings: $e'));
      
      // Add retro water
      _mapController!.addFillLayer(
        'openmaptiles',
        'retro-water',
        FillLayerProperties(
          fillColor: '#4ECDC4', // Retro teal
          fillOpacity: 0.8,
        ),
        sourceLayer: 'water',
        minzoom: 0.0,
        belowLayerId: 'individual-pins-layer',
      ).catchError((e) => debugPrint('Error adding retro water: $e'));
      
      // Add retro parks
      _mapController!.addFillLayer(
        'openmaptiles',
        'retro-parks',
        FillLayerProperties(
          fillColor: '#96CEB4', // Retro mint green
          fillOpacity: 0.7,
        ),
        sourceLayer: 'park',
        minzoom: 10.0,
        belowLayerId: 'individual-pins-layer',
      ).catchError((e) => debugPrint('Error adding retro parks: $e'));
      
      debugPrint('🎮 Retro effects applied successfully');
    } catch (e) {
      debugPrint('Error applying retro effects: $e');
    }
  }
  
  // Apply matrix effects to the map
  void applyMatrixEffects() {
    if (_mapController == null) return;

    debugPrint('💊 Applying matrix effects...');

    try {
      // Remove existing building layers first
      removeAllBuildingLayers();

      // Add pitch black grass/meadow areas
      _mapController!.addFillLayer(
        'openmaptiles',
        'matrix-grass',
        const FillLayerProperties(
          fillColor: '#000000', // Pitch black
          fillOpacity: 1.0,
        ),
        sourceLayer: 'landcover',
        filter: [
          'in',
          ['get', 'class'],
          ['literal', ['grass', 'meadow', 'scrub']]
        ],
        minzoom: 11.0,
        belowLayerId: 'individual-pins-layer',
      ).catchError((e) => debugPrint('Error adding matrix grass: $e'));

      // Add pitch black forest/wood areas
      _mapController!.addFillLayer(
        'openmaptiles',
        'matrix-forest',
        const FillLayerProperties(
          fillColor: '#000000', // Pitch black
          fillOpacity: 1.0,
        ),
        sourceLayer: 'landcover',
        filter: [
          'in',
          ['get', 'class'],
          ['literal', ['wood', 'forest']]
        ],
        minzoom: 10.0,
        belowLayerId: 'individual-pins-layer',
      ).catchError((e) => debugPrint('Error adding matrix forest: $e'));

      // Add pitch black parks
      _mapController!.addFillLayer(
        'openmaptiles',
        'matrix-parks',
        const FillLayerProperties(
          fillColor: '#000000', // Pitch black
          fillOpacity: 1.0,
        ),
        sourceLayer: 'park',
        minzoom: 10.0,
        belowLayerId: 'individual-pins-layer',
      ).catchError((e) => debugPrint('Error adding matrix parks: $e'));

      // Add pitch black road outlines
      _mapController!.addLineLayer(
        'openmaptiles',
        'matrix-road-outlines',
        const LineLayerProperties(
          lineColor: '#000000', // Pitch black outlines
          lineWidth: [
            'interpolate',
            ['linear'],
            ['zoom'],
            10, 2.5,
            14, 6,
            18, 12,
          ],
          lineOpacity: 1.0,
        ),
        sourceLayer: 'transportation',
        minzoom: 10.0,
        belowLayerId: 'individual-pins-layer',
      ).catchError((e) => debugPrint('Error adding matrix road outlines: $e'));

      // Green digital roads (on top of black outlines)
      _mapController!.addLineLayer(
        'openmaptiles',
        'matrix-roads',
        LineLayerProperties(
          lineColor: [
            'case',
            ['==', ['get', 'class'], 'motorway'], '#00ff00',
            ['==', ['get', 'class'], 'trunk'], '#00ff00',
            ['==', ['get', 'class'], 'primary'], '#00cc00',
            ['==', ['get', 'class'], 'secondary'], '#009900',
            '#006600'
          ],
          lineWidth: [
            'interpolate',
            ['linear'],
            ['zoom'],
            10, 0.5,
            14, 2,
            18, 4,
          ],
          lineOpacity: 0.9,
        ),
        sourceLayer: 'transportation',
        minzoom: 10.0,
        belowLayerId: 'individual-pins-layer',
      ).catchError((e) => debugPrint('Error adding matrix roads: $e'));

      // Add matrix buildings
      _mapController!.addFillExtrusionLayer(
        'openmaptiles',
        'matrix-buildings',
        FillExtrusionLayerProperties(
          fillExtrusionColor: [
            'interpolate',
            ['linear'],
            ['get', 'render_height'],
            0, '#00ff00',    // Bright green for short buildings
            20, '#00dd00',   // Medium bright green
            40, '#00bb00',   // Medium green
            60, '#009900',   // Medium green
            80, '#007700',   // Darker green
            100, '#005500',  // Dark green for tall buildings
          ],
          fillExtrusionHeight: [
            'interpolate',
            ['linear'],
            ['zoom'],
            15, ['*', ['get', 'render_height'], 1.8],
            18, ['*', ['get', 'render_height'], 3.5],
          ],
          fillExtrusionBase: [
            'interpolate',
            ['linear'],
            ['zoom'],
            15, 0,
            16, ['get', 'render_min_height']
          ],
          fillExtrusionOpacity: 0.95,
          fillExtrusionVerticalGradient: true,
        ),
        sourceLayer: 'building',
        minzoom: 14.0,
        filter: ['all',
          ['>', ['get', 'render_height'], 0],
          ['!=', ['get', 'type'], 'underground'],
        ],
        belowLayerId: 'individual-pins-layer',
      ).catchError((e) => debugPrint('Error adding matrix buildings: $e'));

      debugPrint('💊 Matrix effects applied successfully');
    } catch (e) {
      debugPrint('Error applying matrix effects: $e');
    }
  }
  
  // Apply vaporwave effects to the map
  void applyVaporwaveEffects() {
    if (_mapController == null) return;
    
    debugPrint('🌊 Applying vaporwave effects...');
    
    try {
      // Remove existing building layers first
      removeAllBuildingLayers();
      
      // Pastel pink/purple roads
      _mapController!.addLineLayer(
        'openmaptiles',
        'vaporwave-roads',
        LineLayerProperties(
          lineColor: [
            'case',
            ['==', ['get', 'class'], 'motorway'], '#ff66cc',
            ['==', ['get', 'class'], 'trunk'], '#ff66cc',
            ['==', ['get', 'class'], 'primary'], '#cc99ff',
            ['==', ['get', 'class'], 'secondary'], '#ffcc99',
            '#ff99cc'
          ],
          lineWidth: [
            'interpolate',
            ['linear'],
            ['zoom'],
            10, 1,
            14, 3,
            18, 6,
          ],
          lineOpacity: 0.8,
        ),
        sourceLayer: 'transportation',
        minzoom: 10.0,
        belowLayerId: 'individual-pins-layer',
      ).catchError((e) => debugPrint('Error adding vaporwave roads: $e'));
      
      // Add vaporwave buildings
      _mapController!.addFillExtrusionLayer(
        'openmaptiles',
        'vaporwave-buildings',
        FillExtrusionLayerProperties(
          fillExtrusionColor: [
            'interpolate',
            ['linear'],
            ['get', 'render_height'],
            0, '#ffccff',
            30, '#ccccff',
            60, '#ffccaa',
            100, '#ffffcc',
          ],
          fillExtrusionHeight: [
            'interpolate',
            ['linear'],
            ['zoom'],
            15, ['*', ['get', 'render_height'], 1.0],
            18, ['*', ['get', 'render_height'], 2.0],
          ],
          fillExtrusionOpacity: 0.6,
        ),
        sourceLayer: 'building',
        minzoom: 15.0,
        filter: ['>', ['get', 'render_height'], 0],
        belowLayerId: 'individual-pins-layer',
      ).catchError((e) => debugPrint('Error adding vaporwave buildings: $e'));
      
      debugPrint('🌊 Vaporwave effects applied successfully');
    } catch (e) {
      debugPrint('Error applying vaporwave effects: $e');
    }
  }
  
  // Apply minimal effects to the map
  void applyMinimalEffects() {
    if (_mapController == null) return;
    
    debugPrint('⚪ Applying minimal effects...');
    
    try {
      // Remove existing building layers first
      removeAllBuildingLayers();
      
      // Simple monochrome roads
      _mapController!.addLineLayer(
        'openmaptiles',
        'minimal-roads',
        LineLayerProperties(
          lineColor: [
            'case',
            ['==', ['get', 'class'], 'motorway'], '#333333',
            ['==', ['get', 'class'], 'trunk'], '#333333',
            ['==', ['get', 'class'], 'primary'], '#666666',
            ['==', ['get', 'class'], 'secondary'], '#999999',
            '#cccccc'
          ],
          lineWidth: [
            'interpolate',
            ['linear'],
            ['zoom'],
            10, 0.5,
            14, 1.5,
            18, 3,
          ],
          lineOpacity: 1.0,
        ),
        sourceLayer: 'transportation',
        minzoom: 10.0,
        belowLayerId: 'individual-pins-layer',
      ).catchError((e) => debugPrint('Error adding minimal roads: $e'));
      
      // Add minimal buildings
      _mapController!.addFillExtrusionLayer(
        'openmaptiles',
        'minimal-buildings',
        FillExtrusionLayerProperties(
          fillExtrusionColor: '#f0f0f0',
          fillExtrusionHeight: [
            'interpolate',
            ['linear'],
            ['zoom'],
            15, ['*', ['get', 'render_height'], 0.8],
            18, ['*', ['get', 'render_height'], 1.5],
          ],
          fillExtrusionOpacity: 0.9,
        ),
        sourceLayer: 'building',
        minzoom: 15.0,
        filter: ['>', ['get', 'render_height'], 0],
        belowLayerId: 'individual-pins-layer',
      ).catchError((e) => debugPrint('Error adding minimal buildings: $e'));
      
      debugPrint('⚪ Minimal effects applied successfully');
    } catch (e) {
      debugPrint('Error applying minimal effects: $e');
    }
  }
  
  // Apply old school effects to the map
  void applyOldSchoolEffects() {
    if (_mapController == null) return;
    
    debugPrint('⚫ Applying old school effects...');
    
    try {
      // Remove existing building layers first
      removeAllBuildingLayers();
      
      // Add classic old school roads
      _mapController!.addLineLayer(
        'openmaptiles',
        'oldschool-roads',
        LineLayerProperties(
          lineColor: [
            'case',
            ['==', ['get', 'class'], 'motorway'], '#4A4A4A',  // Medium gray
            ['==', ['get', 'class'], 'trunk'], '#4A4A4A',     // Medium gray
            ['==', ['get', 'class'], 'primary'], '#5A5A5A',   // Light medium gray
            ['==', ['get', 'class'], 'secondary'], '#6A6A6A', // Light gray
            '#7A7A7A'  // Lighter gray
          ],
          lineWidth: [
            'interpolate',
            ['linear'],
            ['zoom'],
            10, 0.5,
            14, 1.5,
            18, 3,
          ],
          lineOpacity: 0.9,
        ),
        sourceLayer: 'transportation',
        minzoom: 10.0,
        belowLayerId: 'individual-pins-layer',
      ).catchError((e) => debugPrint('Error adding old school roads: $e'));
      
      // Add buildings with black and white gradient for old school style
      _mapController!.addFillExtrusionLayer(
        'openmaptiles',
        'oldschool-buildings',
        FillExtrusionLayerProperties(
          fillExtrusionColor: [
            'interpolate',
            ['linear'],
            ['get', 'render_height'],
            0, '#2a2a2a',    // Dark gray
            20, '#3a3a3a',   // Medium dark gray
            40, '#4a4a4a',   // Medium gray
            60, '#5a5a5a',   // Light medium gray
            80, '#6a6a6a',   // Light gray
            100, '#7a7a7a',  // Lighter gray
          ],
          fillExtrusionHeight: [
            'interpolate',
            ['linear'],
            ['zoom'],
            15, ['*', ['get', 'render_height'], 1.0],
            18, ['*', ['get', 'render_height'], 2.0],
          ],
          fillExtrusionOpacity: 0.8,
          fillExtrusionVerticalGradient: true,
        ),
        sourceLayer: 'building',
        minzoom: 15.0,
        filter: ['>', ['get', 'render_height'], 0],
        belowLayerId: 'individual-pins-layer',
      ).catchError((e) => debugPrint('Error adding old school buildings: $e'));
      
      debugPrint('⚫ Old school effects applied successfully');
    } catch (e) {
      debugPrint('Error applying old school effects: $e');
    }
  }
  
  // Apply watercolor effects to the map
  void applyWatercolorEffects() {
    if (_mapController == null) return;
    
    debugPrint('🎨 Applying watercolor effects...');
    
    try {
      // Remove existing building layers first
      removeAllBuildingLayers();
      
      // Add 3D buildings with vintage watercolor colors
      _mapController!.addFillExtrusionLayer(
        'openmaptiles',
        'watercolor-buildings',
        FillExtrusionLayerProperties(
          fillExtrusionColor: [
            'interpolate',
            ['linear'],
            ['get', 'render_height'],
            0, '#8B4513',    // Saddle brown
            20, '#A0522D',   // Sienna
            40, '#CD853F',   // Peru
            60, '#D2691E',   // Chocolate
            80, '#F4A460',   // Sandy brown
            100, '#DEB887',  // Burlywood
          ],
          fillExtrusionHeight: [
            'interpolate',
            ['linear'],
            ['zoom'],
            15, ['*', ['get', 'render_height'], 1.0],
            18, ['*', ['get', 'render_height'], 2.0],
          ],
          fillExtrusionOpacity: 0.8,
          fillExtrusionVerticalGradient: true,
        ),
        sourceLayer: 'building',
        minzoom: 15.0,
        filter: ['>', ['get', 'render_height'], 0],
        belowLayerId: 'individual-pins-layer',
      ).catchError((e) => debugPrint('Error adding watercolor buildings: $e'));
      
      debugPrint('🎨 Watercolor effects applied successfully');
    } catch (e) {
      debugPrint('Error applying watercolor effects: $e');
    }
  }
  
  // Apply futuristic effects to the map
  void applyFuturisticEffects() {
    if (_mapController == null) return;

    debugPrint('🔮 Applying futuristic effects...');

    try {
      // Remove existing building layers first
      removeAllBuildingLayers();

      // Add pitch black grass/meadow areas
      _mapController!.addFillLayer(
        'openmaptiles',
        'futuristic-grass',
        const FillLayerProperties(
          fillColor: '#000000', // Pitch black
          fillOpacity: 1.0,
        ),
        sourceLayer: 'landcover',
        filter: [
          'in',
          ['get', 'class'],
          ['literal', ['grass', 'meadow', 'scrub']]
        ],
        minzoom: 11.0,
        belowLayerId: 'individual-pins-layer',
      ).catchError((e) => debugPrint('Error adding futuristic grass: $e'));

      // Add pitch black forest/wood areas
      _mapController!.addFillLayer(
        'openmaptiles',
        'futuristic-forest',
        const FillLayerProperties(
          fillColor: '#000000', // Pitch black
          fillOpacity: 1.0,
        ),
        sourceLayer: 'landcover',
        filter: [
          'in',
          ['get', 'class'],
          ['literal', ['wood', 'forest']]
        ],
        minzoom: 10.0,
        belowLayerId: 'individual-pins-layer',
      ).catchError((e) => debugPrint('Error adding futuristic forest: $e'));

      // Add pitch black parks
      _mapController!.addFillLayer(
        'openmaptiles',
        'futuristic-parks',
        const FillLayerProperties(
          fillColor: '#000000', // Pitch black
          fillOpacity: 1.0,
        ),
        sourceLayer: 'park',
        minzoom: 10.0,
        belowLayerId: 'individual-pins-layer',
      ).catchError((e) => debugPrint('Error adding futuristic parks: $e'));

      // Add pitch black road outlines
      _mapController!.addLineLayer(
        'openmaptiles',
        'futuristic-road-outlines',
        const LineLayerProperties(
          lineColor: '#000000', // Pitch black outlines
          lineWidth: [
            'interpolate',
            ['linear'],
            ['zoom'],
            10, 2.5,
            14, 5.0,
            18, 10.0,
          ],
          lineOpacity: 1.0,
        ),
        sourceLayer: 'transportation',
        minzoom: 10.0,
        belowLayerId: 'individual-pins-layer',
      ).catchError((e) => debugPrint('Error adding futuristic road outlines: $e'));

      // Add glowing Tron-blue grid lines (on top of black outlines)
      _mapController!.addLineLayer(
        'openmaptiles',
        'futuristic-grid',
        const LineLayerProperties(
          lineColor: '#00A0FF', // Authentic Tron blue
          lineWidth: [
            'interpolate',
            ['linear'],
            ['zoom'],
            10, 0.5,
            14, 1.5,
            18, 3.0,
          ],
          lineOpacity: 0.8,
          lineBlur: 1.0,
        ),
        sourceLayer: 'transportation',
        minzoom: 10.0,
        belowLayerId: 'individual-pins-layer',
      ).catchError((e) => debugPrint('Error adding futuristic grid: $e'));

      // Add orange accents for major roads
      _mapController!.addLineLayer(
        'openmaptiles',
        'futuristic-accents',
        const LineLayerProperties(
          lineColor: '#FFA500', // Orange
          lineWidth: [
            'interpolate',
            ['linear'],
            ['zoom'],
            10, 0.5,
            14, 1.5,
            18, 3.0,
          ],
          lineOpacity: 0.8,
          lineBlur: 1.0,
        ),
        sourceLayer: 'transportation',
        minzoom: 10.0,
        filter: ['==', ['get', 'class'], 'motorway'],
        belowLayerId: 'individual-pins-layer',
      ).catchError((e) => debugPrint('Error adding futuristic accents: $e'));
      
      // Add transparent futuristic buildings with edge definition
      _mapController!.addFillExtrusionLayer(
        'openmaptiles',
        'futuristic-buildings',
        FillExtrusionLayerProperties(
          fillExtrusionColor: [
            'interpolate',
            ['linear'],
            ['get', 'render_height'],
            0, '#0080ff',    // Lighter blue for short buildings
            20, '#1a8cff',   // Medium light blue
            40, '#3399ff',   // Medium bright blue
            60, '#4da6ff',   // Brighter blue
            80, '#66b3ff',   // Bright light blue
            100, '#80c0ff',  // Lightest blue for tall buildings
          ],
          fillExtrusionHeight: [
            'interpolate',
            ['linear'],
            ['zoom'],
            14, ['*', ['get', 'render_height'], 0.5],
            15, ['*', ['get', 'render_height'], 1.5],
            16, ['*', ['get', 'render_height'], 2.5],
            17, ['*', ['get', 'render_height'], 3.5],
            18, ['*', ['get', 'render_height'], 4.5],
          ],
          fillExtrusionBase: [
            'interpolate',
            ['linear'],
            ['zoom'],
            14, 0,
            15, ['get', 'render_min_height']
          ],
          fillExtrusionOpacity: 0.8,
          fillExtrusionVerticalGradient: true,
        ),
        sourceLayer: 'building',
        minzoom: 14.0,
        filter: ['all',
          ['>', ['get', 'render_height'], 0],
          ['!=', ['get', 'type'], 'underground'],
        ],
        belowLayerId: 'individual-pins-layer',
      ).catchError((e) => debugPrint('Error adding futuristic buildings: $e'));
      
      // Add water with dark futuristic blue-teal
      _mapController!.addFillLayer(
        'openmaptiles',
        'futuristic-water',
        FillLayerProperties(
          fillColor: '#001a2e', // Dark blue-teal to complement the bright blue buildings
          fillOpacity: 1.0,
        ),
        sourceLayer: 'water',
        minzoom: 0.0,
        belowLayerId: 'individual-pins-layer',
      ).catchError((e) => debugPrint('Error adding futuristic water: $e'));
      
      // Add building edges AFTER 3D buildings - use line layers for polygon outlines
      // Glow effect first (bottom layer)
      _mapController!.addLineLayer(
        'openmaptiles',
        'futuristic-building-glow',
        LineLayerProperties(
          lineColor: '#00A0FF', // Tron blue glow
          lineWidth: [
            'interpolate',
            ['linear'],
            ['zoom'],
            14, 4.0,
            16, 6.0,
            18, 8.0,
          ],
          lineOpacity: 0.3, // Semi-transparent for glow effect
          lineBlur: 3.0, // Blur for glow
        ),
        sourceLayer: 'building',
        minzoom: 14.0,
        filter: ['>', ['get', 'render_height'], 0],
        belowLayerId: 'individual-pins-layer',
      ).catchError((e) => debugPrint('Error adding futuristic building glow: $e'));
      
      // Primary building edges using line layer (this works with polygon geometries)
      _mapController!.addLineLayer(
        'openmaptiles',
        'futuristic-building-edges',
        LineLayerProperties(
          lineColor: '#00A0FF', // Tron blue
          lineWidth: [
            'interpolate',
            ['linear'],
            ['zoom'],
            14, 1.0,
            16, 1.5,
            18, 2.0,
          ],
          lineOpacity: 1.0, // Full opacity for iOS compatibility
          lineBlur: 0.0, // Sharp edges
        ),
        sourceLayer: 'building',
        minzoom: 14.0,
        filter: ['>', ['get', 'render_height'], 0], // Simplified filter for iOS
        belowLayerId: 'individual-pins-layer',
      ).catchError((e) => debugPrint('Error adding futuristic building edges: $e'));
      
      // Accent edges for tall buildings - brighter cyan (top layer)
      _mapController!.addLineLayer(
        'openmaptiles',
        'futuristic-building-accent-edges',
        LineLayerProperties(
          lineColor: '#00FFFF', // Bright cyan
          lineWidth: [
            'interpolate',
            ['linear'],
            ['zoom'],
            15, 1.5,
            17, 2.5,
            18, 3.0,
          ],
          lineOpacity: 0.8, // Fixed opacity for iOS
          lineBlur: 0.0, // Sharp edges
        ),
        sourceLayer: 'building',
        minzoom: 15.0,
        filter: ['>', ['get', 'render_height'], 30], // Simplified filter
        belowLayerId: 'individual-pins-layer',
      ).catchError((e) => debugPrint('Error adding futuristic accent building edges: $e'));
      
      debugPrint('🔮 Futuristic effects applied successfully');
    } catch (e) {
      debugPrint('Error applying futuristic effects: $e');
    }
  }

  // Apply neon colors to buildings
  void _applyNeonBuildingColors() {
    if (_mapController == null) return;
    
    debugPrint('🏢 Applying neon building colors...');
    
    try {
      // Remove ALL existing building layers to prevent z-fighting
      removeAllBuildingLayers();
      
      // Add neon 3D buildings with dark purple/teal gradient
      _mapController!.addFillExtrusionLayer(
        'openmaptiles',
        'neon-building-3d',
        FillExtrusionLayerProperties(
          // Dark futuristic colors for buildings
          fillExtrusionColor: [
            'interpolate',
            ['linear'],
            ['get', 'render_height'],
            0, '#1a0033',      // Very dark purple
            10, '#260d4d',     // Dark purple
            20, '#331a66',     // Medium dark purple
            30, '#402680',     // Purple
            40, '#4d3399',     // Lighter purple
            60, '#5940b3',     // Blue-purple
            80, '#664dcc',     // Light blue-purple
            100, '#7359e6',    // Bright purple
            150, '#8066ff',    // Bright violet for skyscrapers
          ],
          fillExtrusionHeight: [
            'interpolate',
            ['linear'],
            ['zoom'],
            14, 0,
            14.5, ['*', ['get', 'render_height'], 0.5],
            15, ['*', ['get', 'render_height'], 1.0],
            15.5, ['*', ['get', 'render_height'], 1.5],
            16, ['*', ['get', 'render_height'], 2.0],
            16.5, ['*', ['get', 'render_height'], 2.25],
            17, ['*', ['get', 'render_height'], 2.5],
            17.5, ['*', ['get', 'render_height'], 2.75],
            18, ['*', ['get', 'render_height'], 3.0],
          ],
          fillExtrusionBase: [
            'interpolate',
            ['linear'],
            ['zoom'],
            14, 0,
            14.5, ['*', ['get', 'render_min_height'], 0.25],
            15, ['*', ['get', 'render_min_height'], 0.5],
            15.5, ['*', ['get', 'render_min_height'], 0.75],
            16, ['get', 'render_min_height']
          ],
          fillExtrusionOpacity: 0.9,
          fillExtrusionVerticalGradient: true,
        ),
        sourceLayer: 'building',
        minzoom: 14.0,
        filter: ['>', ['get', 'render_height'], 0],
        belowLayerId: 'individual-pins-layer',
      ).then((_) {
        debugPrint('🏢 Successfully added neon 3D buildings');
      }).catchError((e) {
        debugPrint('Error adding neon 3D buildings: $e');
      });
      
         } catch (e) {
       debugPrint('Error applying neon building colors: $e');
     }
   }
 }
